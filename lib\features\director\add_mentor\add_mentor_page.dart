import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class AddMentorPage extends StatelessWidget {
  const AddMentorPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Add Mentor',
      subtitle: 'Register new mentors',
      breadcrumbs: ['Dashboard', 'Director', 'Add Mentor'],
      featureName: 'Add Mentor',
    );
  }
}
