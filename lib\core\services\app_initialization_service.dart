import 'package:flutter/foundation.dart';

import '../utils/logger.dart';
import 'storage_service.dart';
import 'token_service.dart';

class AppInitializationService {
  static bool _isInitialized = false;

  /// Initialize all app services
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info('Starting app initialization...');

      // Initialize storage service
      await StorageService.init();
      AppLogger.info('Storage service initialized');

      // Initialize token service
      final tokenService = TokenService();
      await tokenService.initialize();
      AppLogger.info('Token service initialized');

      _isInitialized = true;
      AppLogger.info('App initialization completed successfully');
    } catch (e) {
      AppLogger.error('App initialization failed: $e');
      rethrow;
    }
  }

  /// Check if app is initialized
  static bool get isInitialized => _isInitialized;

  /// Reset initialization state (for testing)
  static void reset() {
    _isInitialized = false;
  }
}
