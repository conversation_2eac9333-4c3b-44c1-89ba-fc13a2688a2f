import 'package:logger/logger.dart';
import '../config/app_config.dart';

class AppLogger {
  static final Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 2,
      errorMethodCount: 8,
      lineLength: 120,
      colors: true,
      printEmojis: true,
      printTime: true,
    ),
    level: AppConfig.isDebug ? Level.debug : Level.info,
  );

  /// Log debug message
  static void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    if (AppConfig.isDebug) {
      _logger.d(message, error: error, stackTrace: stackTrace);
    }
  }

  /// Log info message
  static void info(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  static void fatal(String message, [dynamic error, StackTrace? stackTrace]) {
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log API request
  static void apiRequest(String method, String url, [Map<String, dynamic>? data]) {
    if (AppConfig.isDebug) {
      debug('API Request: $method $url${data != null ? ' - Data: $data' : ''}');
    }
  }

  /// Log API response
  static void apiResponse(String method, String url, int statusCode, [dynamic data]) {
    if (AppConfig.isDebug) {
      debug('API Response: $method $url - Status: $statusCode${data != null ? ' - Data: $data' : ''}');
    }
  }

  /// Log authentication events
  static void auth(String event, [String? details]) {
    info('Auth: $event${details != null ? ' - $details' : ''}');
  }

  /// Log navigation events
  static void navigation(String from, String to) {
    debug('Navigation: $from -> $to');
  }

  /// Log storage operations
  static void storage(String operation, String key, [bool success = true]) {
    debug('Storage: $operation $key - ${success ? 'Success' : 'Failed'}');
  }

  /// Log token operations
  static void token(String operation, [String? details]) {
    info('Token: $operation${details != null ? ' - $details' : ''}');
  }

  /// Log performance metrics
  static void performance(String operation, Duration duration) {
    debug('Performance: $operation took ${duration.inMilliseconds}ms');
  }

  /// Log user actions
  static void userAction(String action, [Map<String, dynamic>? context]) {
    info('User Action: $action${context != null ? ' - Context: $context' : ''}');
  }

  /// Log cache operations
  static void cache(String operation, String key, [bool hit = true]) {
    debug('Cache: $operation $key - ${hit ? 'Hit' : 'Miss'}');
  }

  /// Log background tasks
  static void backgroundTask(String task, [String? status]) {
    info('Background Task: $task${status != null ? ' - $status' : ''}');
  }
}
