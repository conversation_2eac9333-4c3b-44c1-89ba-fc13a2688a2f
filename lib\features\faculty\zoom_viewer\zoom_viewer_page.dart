import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class ZoomViewerPage extends StatelessWidget {
  const ZoomViewerPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Zoom Viewer',
      subtitle: 'Monitor Zoom sessions and meetings',
      breadcrumbs: ['Dashboard', 'Faculty', 'Zoom Viewer'],
      featureName: 'Zoom Viewer',
    );
  }
}
