import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class EventsPage extends StatelessWidget {
  const EventsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Events Management',
      subtitle: 'Organize and manage events',
      breadcrumbs: ['Dashboard', 'Director', 'Events'],
      featureName: 'Events Management',
    );
  }
}
