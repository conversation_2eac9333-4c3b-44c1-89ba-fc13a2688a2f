import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../utils/logger.dart';
import '../config/app_config.dart';

class StorageService {
  static SharedPreferences? _prefs;
  static Box? _hiveBox;
  
  static const String _boxName = 'sasthra_storage';

  /// Initialize storage service
  static Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _hiveBox = await Hive.openBox(_boxName);
      AppLogger.info('StorageService initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize StorageService: $e');
      throw Exception('Storage initialization failed');
    }
  }

  /// Store string value
  static Future<bool> setString(String key, String value) async {
    try {
      return await _prefs!.setString(key, value);
    } catch (e) {
      AppLogger.error('Failed to store string for key $key: $e');
      return false;
    }
  }

  /// Get string value
  static String? getString(String key, {String? defaultValue}) {
    try {
      return _prefs!.getString(key) ?? defaultValue;
    } catch (e) {
      AppLogger.error('Failed to get string for key $key: $e');
      return defaultValue;
    }
  }

  /// Store integer value
  static Future<bool> setInt(String key, int value) async {
    try {
      return await _prefs!.setInt(key, value);
    } catch (e) {
      AppLogger.error('Failed to store int for key $key: $e');
      return false;
    }
  }

  /// Get integer value
  static int? getInt(String key, {int? defaultValue}) {
    try {
      return _prefs!.getInt(key) ?? defaultValue;
    } catch (e) {
      AppLogger.error('Failed to get int for key $key: $e');
      return defaultValue;
    }
  }

  /// Store boolean value
  static Future<bool> setBool(String key, bool value) async {
    try {
      return await _prefs!.setBool(key, value);
    } catch (e) {
      AppLogger.error('Failed to store bool for key $key: $e');
      return false;
    }
  }

  /// Get boolean value
  static bool? getBool(String key, {bool? defaultValue}) {
    try {
      return _prefs!.getBool(key) ?? defaultValue;
    } catch (e) {
      AppLogger.error('Failed to get bool for key $key: $e');
      return defaultValue;
    }
  }

  /// Store object as JSON
  static Future<bool> setObject(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await _prefs!.setString(key, jsonString);
    } catch (e) {
      AppLogger.error('Failed to store object for key $key: $e');
      return false;
    }
  }

  /// Get object from JSON
  static Map<String, dynamic>? getObject(String key) {
    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      AppLogger.error('Failed to get object for key $key: $e');
      return null;
    }
  }

  /// Store list of objects
  static Future<bool> setObjectList(String key, List<Map<String, dynamic>> value) async {
    try {
      final jsonString = jsonEncode(value);
      return await _prefs!.setString(key, jsonString);
    } catch (e) {
      AppLogger.error('Failed to store object list for key $key: $e');
      return false;
    }
  }

  /// Get list of objects
  static List<Map<String, dynamic>>? getObjectList(String key) {
    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;
      final List<dynamic> decoded = jsonDecode(jsonString);
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      AppLogger.error('Failed to get object list for key $key: $e');
      return null;
    }
  }

  /// Remove value by key
  static Future<bool> remove(String key) async {
    try {
      return await _prefs!.remove(key);
    } catch (e) {
      AppLogger.error('Failed to remove key $key: $e');
      return false;
    }
  }

  /// Check if key exists
  static bool containsKey(String key) {
    try {
      return _prefs!.containsKey(key);
    } catch (e) {
      AppLogger.error('Failed to check key $key: $e');
      return false;
    }
  }

  /// Clear all stored data
  static Future<bool> clearAll() async {
    try {
      await _prefs!.clear();
      await _hiveBox!.clear();
      AppLogger.info('All storage data cleared');
      return true;
    } catch (e) {
      AppLogger.error('Failed to clear all storage: $e');
      return false;
    }
  }

  /// Hive storage methods for complex objects
  
  /// Store data in Hive box
  static Future<void> putHive(String key, dynamic value) async {
    try {
      await _hiveBox!.put(key, value);
    } catch (e) {
      AppLogger.error('Failed to store in Hive for key $key: $e');
    }
  }

  /// Get data from Hive box
  static T? getHive<T>(String key, {T? defaultValue}) {
    try {
      return _hiveBox!.get(key, defaultValue: defaultValue);
    } catch (e) {
      AppLogger.error('Failed to get from Hive for key $key: $e');
      return defaultValue;
    }
  }

  /// Delete data from Hive box
  static Future<void> deleteHive(String key) async {
    try {
      await _hiveBox!.delete(key);
    } catch (e) {
      AppLogger.error('Failed to delete from Hive for key $key: $e');
    }
  }

  /// Check if Hive box contains key
  static bool containsHiveKey(String key) {
    try {
      return _hiveBox!.containsKey(key);
    } catch (e) {
      AppLogger.error('Failed to check Hive key $key: $e');
      return false;
    }
  }

  /// Get all keys from storage
  static Set<String> getAllKeys() {
    try {
      return _prefs!.getKeys();
    } catch (e) {
      AppLogger.error('Failed to get all keys: $e');
      return <String>{};
    }
  }

  /// Get storage size information
  static Future<Map<String, int>> getStorageInfo() async {
    try {
      final prefsKeys = _prefs!.getKeys().length;
      final hiveKeys = _hiveBox!.keys.length;
      
      return {
        'preferences_keys': prefsKeys,
        'hive_keys': hiveKeys,
        'total_keys': prefsKeys + hiveKeys,
      };
    } catch (e) {
      AppLogger.error('Failed to get storage info: $e');
      return {'error': 1};
    }
  }
}
