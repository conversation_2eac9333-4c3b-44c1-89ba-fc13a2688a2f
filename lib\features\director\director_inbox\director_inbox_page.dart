import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class DirectorInboxPage extends StatelessWidget {
  const DirectorInboxPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Director Inbox',
      subtitle: 'Manage your messages and notifications',
      breadcrumbs: ['Dash<PERSON>', 'Director', 'Director Inbox'],
      featureName: 'Director Inbox',
    );
  }
}
