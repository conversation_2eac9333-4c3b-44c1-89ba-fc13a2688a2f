import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';

import '../config/app_config.dart';
import '../models/auth_models.dart';
import '../exceptions/api_exceptions.dart';
import '../utils/logger.dart';
import 'api_service.dart';
import 'token_service.dart';
import 'storage_service.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  final TokenService _tokenService = TokenService();

  AuthState _state = AuthState.initial;
  UserData? _currentUser;
  String? _errorMessage;
  Timer? _sessionTimer;

  // Navigation callback for logout
  static GoRouter? _router;

  // Set router instance for navigation
  static void setRouter(GoRouter router) {
    _router = router;
  }

  // Getters
  AuthState get state => _state;
  UserData? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _state == AuthState.authenticated && _currentUser != null;
  bool get isLoading => _state == AuthState.loading;

  /// Initialize authentication service
  Future<void> initialize() async {
    try {
      AppLogger.auth('Initializing AuthService');
      
      // Initialize token service
      await _tokenService.initialize();
      
      // Check if user is already authenticated
      await _checkAuthenticationStatus();
      
      AppLogger.auth('AuthService initialized successfully');
    } catch (e) {
      AppLogger.error('Failed to initialize AuthService: $e');
      _setState(AuthState.unauthenticated);
    }
  }

  /// Check current authentication status
  Future<void> _checkAuthenticationStatus() async {
    try {
      final userData = StorageService.getObject(AppConfig.userDataKey);
      if (userData == null) {
        _setState(AuthState.unauthenticated);
        return;
      }

      final isTokenValid = await _tokenService.isTokenValid();
      if (!isTokenValid) {
        await logout();
        return;
      }

      _currentUser = UserData.fromJson(userData);
      _setState(AuthState.authenticated);
      _startSessionMonitoring();
      
      AppLogger.auth('User authenticated from storage', _currentUser?.username);
    } catch (e) {
      AppLogger.error('Authentication status check failed: $e');
      await logout();
    }
  }

  /// Login with username and password
  Future<AuthResult> login(String username, String password) async {
    try {
      _setState(AuthState.loading);
      AppLogger.auth('Login attempt', username);

      final response = await _apiService.login(username, password);
      
      _setState(AuthState.otpRequired);
      AppLogger.auth('OTP sent successfully', username);
      
      return AuthResult.success(message: response.message);
    } on ApiException catch (e) {
      _setError(e.message);
      AppLogger.error('Login failed: ${e.message}');
      return AuthResult.failure(message: e.message, error: e);
    } catch (e) {
      final message = 'Login failed: ${e.toString()}';
      _setError(message);
      AppLogger.error(message);
      return AuthResult.failure(message: message, error: Exception(e.toString()));
    }
  }

  /// Verify OTP and complete authentication
  Future<AuthResult> verifyOtp(String otp) async {
    try {
      _setState(AuthState.loading);
      AppLogger.auth('OTP verification attempt');

      final response = await _apiService.verifyOtp(otp);
      
      // Store authentication data
      await _tokenService.storeToken(response.token);
      await _tokenService.storeSessionId(response.activeSessionId);
      await StorageService.setObject(AppConfig.userDataKey, response.user.toJson());
      
      _currentUser = response.user;
      _setState(AuthState.authenticated);
      _startSessionMonitoring();
      
      AppLogger.auth('Authentication successful', _currentUser?.username);
      
      return AuthResult.success(
        message: 'Login successful',
        authResponse: response,
      );
    } on ApiException catch (e) {
      _setError(e.message);
      AppLogger.error('OTP verification failed: ${e.message}');
      return AuthResult.failure(message: e.message, error: e);
    } catch (e) {
      final message = 'OTP verification failed: ${e.toString()}';
      _setError(message);
      AppLogger.error(message);
      return AuthResult.failure(message: message, error: Exception(e.toString()));
    }
  }

  /// Logout user
  Future<void> logout({bool fromServer = true}) async {
    try {
      AppLogger.auth('Logout initiated', _currentUser?.username);

      if (fromServer && _currentUser != null) {
        // Attempt to logout from server
        await _apiService.logout(_currentUser!.id);
      }

      // Clear local data
      await _tokenService.clearTokens();
      _currentUser = null;
      _errorMessage = null;
      _stopSessionMonitoring();

      _setState(AuthState.unauthenticated);

      // Navigate to login page
      if (_router != null) {
        _router!.go('/auth/login');
      }

      AppLogger.auth('Logout completed');
    } catch (e) {
      AppLogger.error('Logout error: $e');
      // Even if server logout fails, clear local data
      await _tokenService.clearTokens();
      _currentUser = null;
      _setState(AuthState.unauthenticated);

      // Navigate to login page even on error
      if (_router != null) {
        _router!.go('/auth/login');
      }
    }
  }

  /// Force logout due to token expiry or session issues
  Future<void> forceLogout({String? reason}) async {
    AppLogger.auth('Force logout', reason ?? 'Token/Session expired');
    await logout(fromServer: false);
  }

  /// Validate current session
  Future<bool> validateSession() async {
    try {
      if (_currentUser == null) return false;
      
      final sessionId = await _tokenService.getSessionId();
      if (sessionId == null) return false;
      
      final isValid = await _apiService.validateSession(_currentUser!.id, sessionId);
      
      if (!isValid) {
        AppLogger.warning('Session validation failed');
        await forceLogout(reason: 'Session validation failed');
        return false;
      }
      
      return true;
    } catch (e) {
      AppLogger.error('Session validation error: $e');
      await forceLogout(reason: 'Session validation error');
      return false;
    }
  }

  /// Start session monitoring
  void _startSessionMonitoring() {
    _stopSessionMonitoring();
    
    // Check session every 5 minutes
    _sessionTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      final isValid = await validateSession();
      if (!isValid) {
        timer.cancel();
      }
    });
    
    AppLogger.auth('Session monitoring started');
  }

  /// Stop session monitoring
  void _stopSessionMonitoring() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  /// Set authentication state
  void _setState(AuthState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
      AppLogger.auth('State changed to: ${newState.name}');
    }
  }

  /// Set error state
  void _setError(String message) {
    _errorMessage = message;
    _setState(AuthState.error);
  }

  /// Clear error
  void clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _setState(_currentUser != null ? AuthState.authenticated : AuthState.unauthenticated);
    }
  }

  /// Get user role
  String? get userRole => _currentUser?.role;

  /// Check if user has specific role
  bool hasRole(String role) => _currentUser?.role == role;

  /// Get user display name
  String get userDisplayName => _currentUser?.displayName ?? 'User';

  /// Refresh authentication state
  Future<void> refresh() async {
    if (_currentUser != null) {
      await _checkAuthenticationStatus();
    }
  }

  @override
  void dispose() {
    _stopSessionMonitoring();
    _tokenService.dispose();
    super.dispose();
  }
}
