import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class KotaTeacherOverviewPage extends StatefulWidget {
  const KotaTeacherOverviewPage({Key? key}) : super(key: key);

  @override
  State<KotaTeacherOverviewPage> createState() => _KotaTeacherOverviewPageState();
}

class _KotaTeacherOverviewPageState extends State<KotaTeacherOverviewPage> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadOverviewData();
  }

  Future<void> _loadOverviewData() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() => _isLoading = false);
    AppLogger.userAction('Kota Teacher Overview loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Overview',
      subtitle: 'Kota Teacher Dashboard',
      breadcrumbs: const ['Dashboard', 'Kota Teacher', 'Overview'],
      isLoading: _isLoading,
      child: _buildOverviewContent(),
    );
  }

  Widget _buildOverviewContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 20),
          _buildStatsGrid(),
          const SizedBox(height: 20),
          _buildQuickActions(),
          const SizedBox(height: 20),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Welcome back!',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Ready to inspire and educate students today?',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                Icons.school,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Kota Teacher Portal',
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.white.withOpacity(0.8),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn().slideY(begin: 0.2);
  }

  Widget _buildStatsGrid() {
    final stats = [
      {'title': 'Active Students', 'value': '156', 'icon': Icons.people, 'color': Colors.blue},
      {'title': 'Live Sessions', 'value': '8', 'icon': Icons.video_call, 'color': Colors.green},
      {'title': 'Scheduled Events', 'value': '12', 'icon': Icons.event, 'color': Colors.orange},
      {'title': 'Centers Mapped', 'value': '5', 'icon': Icons.location_on, 'color': Colors.purple},
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.5,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.borderColor,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(
                    stat['icon'] as IconData,
                    color: stat['color'] as Color,
                    size: 24,
                  ),
                  Text(
                    stat['value'] as String,
                    style: AppTheme.headingLarge.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Text(
                stat['title'] as String,
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ).animate(delay: Duration(milliseconds: 100 * index))
            .fadeIn()
            .slideX(begin: 0.2);
      },
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {'title': 'Start Live Session', 'icon': Icons.videocam, 'color': Colors.red},
      {'title': 'Schedule Event', 'icon': Icons.event_note, 'color': Colors.blue},
      {'title': 'View Centers', 'icon': Icons.location_city, 'color': Colors.green},
      {'title': 'Join Zoom Call', 'icon': Icons.video_call, 'color': Colors.purple},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 3,
          ),
          itemCount: actions.length,
          itemBuilder: (context, index) {
            final action = actions[index];
            return ElevatedButton.icon(
              onPressed: () {
                AppLogger.userAction('Quick action tapped', {'title': action['title']});
                _handleQuickAction(context, action['title'] as String);
              },
              icon: Icon(
                action['icon'] as IconData,
                size: 18,
              ),
              label: Text(
                action['title'] as String,
                style: const TextStyle(fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: (action['color'] as Color).withOpacity(0.1),
                foregroundColor: action['color'] as Color,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ).animate(delay: Duration(milliseconds: 150 * index))
                .fadeIn()
                .scale(begin: const Offset(0.8, 0.8));
          },
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    final activities = [
      {'title': 'Live session completed', 'time': '2 hours ago', 'icon': Icons.check_circle},
      {'title': 'New event scheduled', 'time': '4 hours ago', 'icon': Icons.event},
      {'title': 'Center mapping updated', 'time': '1 day ago', 'icon': Icons.location_on},
      {'title': 'Zoom call attended', 'time': '2 days ago', 'icon': Icons.video_call},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.borderColor,
              width: 1,
            ),
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activities.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: AppTheme.borderColor,
            ),
            itemBuilder: (context, index) {
              final activity = activities[index];
              return ListTile(
                leading: Icon(
                  activity['icon'] as IconData,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                title: Text(
                  activity['title'] as String,
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppTheme.textPrimary,
                  ),
                ),
                subtitle: Text(
                  activity['time'] as String,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                trailing: Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: AppTheme.textTertiary,
                ),
              ).animate(delay: Duration(milliseconds: 100 * index))
                  .fadeIn()
                  .slideX(begin: 0.1);
            },
          ),
        ),
      ],
    );
  }

  void _handleQuickAction(BuildContext context, String actionTitle) {
    switch (actionTitle) {
      case 'View Centers':
        context.go('/dashboard/kota-teacher/mapping-centers');
        break;
      case 'Start Live Session':
        context.go('/dashboard/kota-teacher/live-streaming');
        break;
      case 'Schedule Event':
        context.go('/dashboard/kota-teacher/schedule-events');
        break;
      case 'Join Zoom Call':
        context.go('/dashboard/kota-teacher/zoom-call');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$actionTitle feature coming soon!'),
            behavior: SnackBarBehavior.floating,
          ),
        );
    }
  }
}
