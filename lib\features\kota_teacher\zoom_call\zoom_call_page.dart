import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class ZoomCallPage extends StatefulWidget {
  const ZoomCallPage({Key? key}) : super(key: key);

  @override
  State<ZoomCallPage> createState() => _ZoomCallPageState();
}

class _ZoomCallPageState extends State<ZoomCallPage> {
  bool _isLoading = false;
  List<Map<String, dynamic>> _scheduledMeetings = [];
  List<Map<String, dynamic>> _recentMeetings = [];

  @override
  void initState() {
    super.initState();
    _loadMeetings();
  }

  Future<void> _loadMeetings() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _scheduledMeetings = [
        {
          'id': '1',
          'title': 'Physics Doubt Session',
          'meetingId': '123-456-789',
          'date': DateTime.now().add(const Duration(hours: 2)),
          'duration': 60,
          'participants': 25,
          'status': 'Scheduled',
          'password': 'physics123',
        },
        {
          'id': '2',
          'title': 'Mathematics Workshop',
          'meetingId': '987-654-321',
          'date': DateTime.now().add(const Duration(days: 1)),
          'duration': 90,
          'participants': 30,
          'status': 'Scheduled',
          'password': 'math456',
        },
        {
          'id': '3',
          'title': 'Chemistry Lab Discussion',
          'meetingId': '456-789-123',
          'date': DateTime.now().add(const Duration(days: 2)),
          'duration': 45,
          'participants': 20,
          'status': 'Scheduled',
          'password': 'chem789',
        },
      ];
      
      _recentMeetings = [
        {
          'id': '4',
          'title': 'Biology Revision',
          'meetingId': '111-222-333',
          'date': DateTime.now().subtract(const Duration(hours: 3)),
          'duration': 75,
          'participants': 28,
          'status': 'Completed',
          'recording': true,
        },
        {
          'id': '5',
          'title': 'JEE Strategy Session',
          'meetingId': '444-555-666',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'duration': 120,
          'participants': 45,
          'status': 'Completed',
          'recording': true,
        },
      ];
      
      _isLoading = false;
    });
    
    AppLogger.userAction('Zoom meetings loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Zoom Call',
      subtitle: 'Manage video conferences and meetings',
      breadcrumbs: const ['Dashboard', 'Kota Teacher', 'Zoom Call'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadMeetings,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateMeetingDialog,
        icon: const Icon(Icons.video_call),
        label: const Text('New Meeting'),
        backgroundColor: AppTheme.primaryColor,
      ),
      child: _buildZoomContent(),
    );
  }

  Widget _buildZoomContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickJoinCard(),
          const SizedBox(height: 20),
          _buildMeetingStats(),
          const SizedBox(height: 20),
          _buildScheduledMeetings(),
          const SizedBox(height: 20),
          _buildRecentMeetings(),
        ],
      ),
    );
  }

  Widget _buildQuickJoinCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF2D8CFF), Color(0xFF1E6FFF)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF2D8CFF).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_call,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            'Quick Join Meeting',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Enter a meeting ID to join instantly',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Enter Meeting ID',
                    hintStyle: TextStyle(color: Colors.grey[600]),
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton(
                onPressed: _quickJoinMeeting,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF2D8CFF),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Join'),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildMeetingStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Scheduled',
            _scheduledMeetings.length.toString(),
            Icons.schedule,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Completed',
            _recentMeetings.length.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'This Week',
            '8',
            Icons.calendar_today,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildScheduledMeetings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Scheduled Meetings',
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                AppLogger.userAction('View all scheduled meetings');
              },
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (_scheduledMeetings.isEmpty)
          _buildEmptyMeetings('No scheduled meetings')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _scheduledMeetings.length,
            itemBuilder: (context, index) {
              final meeting = _scheduledMeetings[index];
              return _buildMeetingCard(meeting, index, true);
            },
          ),
      ],
    );
  }

  Widget _buildRecentMeetings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Meetings',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (_recentMeetings.isEmpty)
          _buildEmptyMeetings('No recent meetings')
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentMeetings.length,
            itemBuilder: (context, index) {
              final meeting = _recentMeetings[index];
              return _buildMeetingCard(meeting, index, false);
            },
          ),
      ],
    );
  }

  Widget _buildMeetingCard(Map<String, dynamic> meeting, int index, bool isScheduled) {
    final meetingDate = meeting['date'] as DateTime;
    final isUpcoming = meetingDate.isAfter(DateTime.now());
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isUpcoming ? Colors.blue.withOpacity(0.1) : Colors.green.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            isUpcoming ? Icons.video_call : Icons.videocam_off,
            color: isUpcoming ? Colors.blue : Colors.green,
            size: 24,
          ),
        ),
        title: Text(
          meeting['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.access_time, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  _formatMeetingDate(meetingDate),
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const SizedBox(width: 16),
                Icon(Icons.timer, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${meeting['duration']} min',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.people, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${meeting['participants']} participants',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const Spacer(),
                if (meeting['recording'] == true)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.fiber_manual_record, size: 8, color: Colors.red),
                        const SizedBox(width: 4),
                        Text(
                          'Recorded',
                          style: AppTheme.bodySmall.copyWith(
                            color: Colors.red,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            if (isScheduled) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.key, size: 14, color: AppTheme.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'ID: ${meeting['meetingId']}',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                  ),
                ],
              ),
            ],
          ],
        ),
        trailing: isScheduled
            ? ElevatedButton(
                onPressed: () => _joinMeeting(meeting),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(60, 32),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                child: const Text('Join', style: TextStyle(fontSize: 12)),
              )
            : Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: AppTheme.textTertiary,
              ),
        onTap: () => _showMeetingDetails(meeting),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildEmptyMeetings(String message) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_call_outlined,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Schedule your first meeting to get started',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  String _formatMeetingDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now);
    
    if (difference.inDays == 0) {
      if (difference.isNegative) {
        return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')} (Completed)';
      } else {
        return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      }
    } else if (difference.inDays == 1) {
      return 'Tomorrow ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == -1) {
      return 'Yesterday ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    }
  }

  void _quickJoinMeeting() {
    AppLogger.userAction('Quick join meeting attempted');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Quick join feature will be integrated with Zoom SDK'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _joinMeeting(Map<String, dynamic> meeting) {
    AppLogger.userAction('Join meeting', meeting['title']);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Joining ${meeting['title']}...'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _showMeetingDetails(Map<String, dynamic> meeting) {
    AppLogger.userAction('Meeting details viewed', meeting['title']);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    meeting['title'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Meeting ID: ${meeting['meetingId']}',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Meeting details, participant management, and recording options will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateMeetingDialog() {
    AppLogger.userAction('Create meeting dialog opened');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Schedule New Meeting'),
        content: const Text('Meeting scheduling functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Schedule meeting feature coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: const Text('Schedule'),
          ),
        ],
      ),
    );
  }
}
