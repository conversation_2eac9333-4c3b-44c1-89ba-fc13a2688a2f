import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:workmanager/workmanager.dart';
import 'package:flutter_web_plugins/url_strategy.dart';

import 'core/config/app_config.dart';
import 'core/services/app_initialization_service.dart';
import 'core/services/auth_service.dart';
import 'core/services/storage_service.dart';
import 'core/services/token_service.dart';
import 'core/theme/app_theme.dart';
import 'core/router/app_router.dart';
import 'core/utils/logger.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Configure URL strategy for web (removes # from URLs)
  if (kIsWeb) {
    usePathUrlStrategy();
  }

  // Initialize Hive for local storage
  await Hive.initFlutter();

  // Initialize all app services
  await AppInitializationService.initialize();

  // Initialize Workmanager for background tasks (only on mobile platforms)
  if (!kIsWeb) {
    try {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: AppConfig.isDebug,
      );

      // Register periodic token refresh task
      await Workmanager().registerPeriodicTask(
        "token-refresh-task",
        "tokenRefresh",
        frequency: const Duration(hours: 4), // Check every 4 hours
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to initialize Workmanager: $e');
    }
  }
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  runApp(
    ProviderScope(
      child: SashtraApp(),
    ),
  );
}

// Background task callback dispatcher
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case "tokenRefresh":
        await _handleTokenRefresh();
        break;
    }
    return Future.value(true);
  });
}

Future<void> _handleTokenRefresh() async {
  try {
    // Initialize services if not already done
    if (!AppInitializationService.isInitialized) {
      await AppInitializationService.initialize();
    }

    final tokenService = TokenService();
    final isValid = await tokenService.validateAndRefreshToken();

    if (!isValid) {
      // Token is invalid or expired, clear storage and redirect to login
      await StorageService.clearAll();
      AppLogger.info('Token expired, user will be redirected to login');
    }
  } catch (e) {
    AppLogger.error('Background token refresh failed: $e');
  }
}

class SashtraApp extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(appRouterProvider);

    // Set router in AuthService for logout navigation
    AuthService.setRouter(router);

    return MaterialApp.router(
      title: 'Sasthra',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      routerConfig: router,
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(1.0), // Prevent text scaling
          ),
          child: child!,
        );
      },
    );
  }
}
