import 'package:json_annotation/json_annotation.dart';

part 'menu_models.g.dart';

@JsonSerializable()
class MenuItem {
  final String href;
  final String name;
  final int orderby;
  final String role;
  final List<MenuItem>? submenu;
  final String uuid;

  MenuItem({
    required this.href,
    required this.name,
    required this.orderby,
    required this.role,
    this.submenu,
    required this.uuid,
  });

  factory MenuItem.fromJson(Map<String, dynamic> json) =>
      _$MenuItemFromJson(json);

  Map<String, dynamic> toJson() => _$MenuItemToJson(this);

  /// Get icon name based on menu item name
  String get iconName {
    final lowerName = name.toLowerCase();
    
    if (lowerName.contains('overview') || lowerName.contains('dashboard')) {
      return 'dashboard';
    } else if (lowerName.contains('live') || lowerName.contains('viewer')) {
      return 'live_tv';
    } else if (lowerName.contains('paper') || lowerName.contains('test')) {
      return 'assignment';
    } else if (lowerName.contains('evaluator') || lowerName.contains('result')) {
      return 'assessment';
    } else if (lowerName.contains('ai') || lowerName.contains('tutor')) {
      return 'psychology';
    } else if (lowerName.contains('zoom')) {
      return 'video_call';
    } else if (lowerName.contains('student')) {
      return 'school';
    } else if (lowerName.contains('faculty') || lowerName.contains('teacher')) {
      return 'person';
    } else if (lowerName.contains('center')) {
      return 'location_on';
    } else if (lowerName.contains('report')) {
      return 'analytics';
    } else if (lowerName.contains('setting')) {
      return 'settings';
    } else if (lowerName.contains('profile')) {
      return 'account_circle';
    } else if (lowerName.contains('notification')) {
      return 'notifications';
    } else if (lowerName.contains('help') || lowerName.contains('support')) {
      return 'help';
    } else {
      return 'menu';
    }
  }

  /// Get route path for navigation
  String get routePath {
    // Convert href to route path
    String path = href;

    // Remove /sasthra prefix if present
    if (path.startsWith('/sasthra')) {
      path = path.replaceFirst('/sasthra', '');
    }

    // Convert 'teacher' role to 'kota_teacher' for proper routing
    if (path.startsWith('/teacher/')) {
      path = path.replaceFirst('/teacher/', '/kota_teacher/');
    }

    // Convert underscores to hyphens for consistency with router
    path = path.replaceAll('_', '-');

    // Ensure path starts with /
    if (!path.startsWith('/')) {
      path = '/$path';
    }

    // Prefix with /dashboard for proper routing
    if (!path.startsWith('/dashboard')) {
      path = '/dashboard$path';
    }

    return path;
  }

  /// Check if this menu item has submenu
  bool get hasSubmenu => submenu != null && submenu!.isNotEmpty;

  /// Get sorted submenu items
  List<MenuItem> get sortedSubmenu {
    if (submenu == null) return [];
    final sorted = List<MenuItem>.from(submenu!);
    sorted.sort((a, b) => a.orderby.compareTo(b.orderby));
    return sorted;
  }
}

@JsonSerializable()
class MenuCache {
  final String role;
  final List<MenuItem> items;
  final DateTime cachedAt;
  final Duration cacheExpiry;

  MenuCache({
    required this.role,
    required this.items,
    required this.cachedAt,
    this.cacheExpiry = const Duration(hours: 24),
  });

  factory MenuCache.fromJson(Map<String, dynamic> json) =>
      _$MenuCacheFromJson(json);

  Map<String, dynamic> toJson() => _$MenuCacheToJson(this);

  /// Check if cache is expired
  bool get isExpired {
    return DateTime.now().difference(cachedAt) > cacheExpiry;
  }

  /// Get sorted menu items
  List<MenuItem> get sortedItems {
    final sorted = List<MenuItem>.from(items);
    sorted.sort((a, b) => a.orderby.compareTo(b.orderby));
    return sorted;
  }

  /// Create new cache with updated items
  MenuCache copyWith({
    String? role,
    List<MenuItem>? items,
    DateTime? cachedAt,
    Duration? cacheExpiry,
  }) {
    return MenuCache(
      role: role ?? this.role,
      items: items ?? this.items,
      cachedAt: cachedAt ?? this.cachedAt,
      cacheExpiry: cacheExpiry ?? this.cacheExpiry,
    );
  }
}

/// Menu state enum
enum MenuState {
  initial,
  loading,
  loaded,
  error,
  cached,
}

/// Menu result class
class MenuResult {
  final bool success;
  final List<MenuItem>? items;
  final String? error;
  final bool fromCache;

  MenuResult({
    required this.success,
    this.items,
    this.error,
    this.fromCache = false,
  });

  factory MenuResult.success({
    required List<MenuItem> items,
    bool fromCache = false,
  }) {
    return MenuResult(
      success: true,
      items: items,
      fromCache: fromCache,
    );
  }

  factory MenuResult.failure({
    required String error,
  }) {
    return MenuResult(
      success: false,
      error: error,
    );
  }
}

/// Navigation item for drawer
class NavigationItem {
  final String title;
  final String icon;
  final String route;
  final bool isSelected;
  final List<NavigationItem>? children;

  NavigationItem({
    required this.title,
    required this.icon,
    required this.route,
    this.isSelected = false,
    this.children,
  });

  /// Create from MenuItem
  factory NavigationItem.fromMenuItem(MenuItem item, {bool isSelected = false}) {
    return NavigationItem(
      title: item.name,
      icon: item.iconName,
      route: item.routePath,
      isSelected: isSelected,
      children: item.hasSubmenu
          ? item.sortedSubmenu
              .map((subItem) => NavigationItem.fromMenuItem(subItem))
              .toList()
          : null,
    );
  }

  /// Check if has children
  bool get hasChildren => children != null && children!.isNotEmpty;

  /// Copy with new selection state
  NavigationItem copyWith({
    String? title,
    String? icon,
    String? route,
    bool? isSelected,
    List<NavigationItem>? children,
  }) {
    return NavigationItem(
      title: title ?? this.title,
      icon: icon ?? this.icon,
      route: route ?? this.route,
      isSelected: isSelected ?? this.isSelected,
      children: children ?? this.children,
    );
  }
}
