import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class ListMentorPage extends StatelessWidget {
  const ListMentorPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'List Mentors',
      subtitle: 'View and manage mentors',
      breadcrumbs: ['Dashboard', 'Director', 'List Mentors'],
      featureName: 'List Mentors',
    );
  }
}
