import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class ListStudentsPage extends StatelessWidget {
  const ListStudentsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'List Students',
      subtitle: 'View and manage all students',
      breadcrumbs: ['Dashboard', 'Center Counselor', 'List Students'],
      featureName: 'List Students',
    );
  }
}
