import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class LiveViewerPage extends StatelessWidget {
  const LiveViewerPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Live Viewer',
      subtitle: 'Monitor live classes and sessions',
      breadcrumbs: ['Dashboard', 'Faculty', 'Live Viewer'],
      featureName: 'Live Viewer',
    );
  }
}
