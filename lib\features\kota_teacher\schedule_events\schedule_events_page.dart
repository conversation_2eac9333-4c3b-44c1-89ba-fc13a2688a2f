import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class ScheduleEventsPage extends StatefulWidget {
  const ScheduleEventsPage({Key? key}) : super(key: key);

  @override
  State<ScheduleEventsPage> createState() => _ScheduleEventsPageState();
}

class _ScheduleEventsPageState extends State<ScheduleEventsPage> with TickerProviderStateMixin {
  bool _isLoading = false;
  List<Map<String, dynamic>> _events = [];
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadEvents();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadEvents() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _events = [
        {
          'id': '1',
          'title': 'Physics Live Session',
          'description': 'Mechanics and Thermodynamics',
          'date': DateTime.now().add(const Duration(days: 1)),
          'duration': '2 hours',
          'type': 'Live Session',
          'status': 'Scheduled',
          'attendees': 45,
        },
        {
          'id': '2',
          'title': 'Mathematics Workshop',
          'description': 'Calculus Problem Solving',
          'date': DateTime.now().add(const Duration(days: 2)),
          'duration': '1.5 hours',
          'type': 'Workshop',
          'status': 'Scheduled',
          'attendees': 32,
        },
        {
          'id': '3',
          'title': 'Chemistry Doubt Session',
          'description': 'Organic Chemistry Q&A',
          'date': DateTime.now().add(const Duration(days: 3)),
          'duration': '1 hour',
          'type': 'Doubt Session',
          'status': 'Scheduled',
          'attendees': 28,
        },
        {
          'id': '4',
          'title': 'Biology Revision',
          'description': 'NEET Biology Revision',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'duration': '2 hours',
          'type': 'Revision',
          'status': 'Completed',
          'attendees': 38,
        },
        {
          'id': '5',
          'title': 'Mock Test Discussion',
          'description': 'JEE Main Mock Test Analysis',
          'date': DateTime.now().subtract(const Duration(days: 2)),
          'duration': '1 hour',
          'type': 'Discussion',
          'status': 'Completed',
          'attendees': 42,
        },
      ];
      _isLoading = false;
    });
    
    AppLogger.userAction('Schedule Events loaded');
  }

  List<Map<String, dynamic>> get _upcomingEvents {
    return _events.where((event) => 
      event['date'].isAfter(DateTime.now()) && event['status'] == 'Scheduled'
    ).toList()..sort((a, b) => a['date'].compareTo(b['date']));
  }

  List<Map<String, dynamic>> get _todayEvents {
    final today = DateTime.now();
    return _events.where((event) {
      final eventDate = event['date'] as DateTime;
      return eventDate.year == today.year &&
             eventDate.month == today.month &&
             eventDate.day == today.day;
    }).toList();
  }

  List<Map<String, dynamic>> get _pastEvents {
    return _events.where((event) => 
      event['date'].isBefore(DateTime.now()) || event['status'] == 'Completed'
    ).toList()..sort((a, b) => b['date'].compareTo(a['date']));
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Schedule Events',
      subtitle: 'Manage your teaching schedule',
      breadcrumbs: const ['Dashboard', 'Kota Teacher', 'Schedule Events'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadEvents,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateEventDialog,
        icon: const Icon(Icons.add),
        label: const Text('New Event'),
        backgroundColor: AppTheme.primaryColor,
      ),
      child: _buildEventsContent(),
    );
  }

  Widget _buildEventsContent() {
    return Column(
      children: [
        _buildStatsCards(),
        const SizedBox(height: 20),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildEventsList(_upcomingEvents, 'No upcoming events'),
              _buildEventsList(_todayEvents, 'No events today'),
              _buildEventsList(_pastEvents, 'No past events'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCards() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Upcoming',
              _upcomingEvents.length.toString(),
              Icons.schedule,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Today',
              _todayEvents.length.toString(),
              Icons.today,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Completed',
              _pastEvents.length.toString(),
              Icons.check_circle,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTheme.headingMedium.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.all(4),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        labelStyle: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTheme.bodyMedium,
        tabs: const [
          Tab(text: 'Upcoming'),
          Tab(text: 'Today'),
          Tab(text: 'Past'),
        ],
      ),
    ).animate().fadeIn().slideY(begin: -0.1);
  }

  Widget _buildEventsList(List<Map<String, dynamic>> events, String emptyMessage) {
    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: AppTheme.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: AppTheme.headingSmall.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first event to get started',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: events.length,
      itemBuilder: (context, index) {
        final event = events[index];
        return _buildEventCard(event, index);
      },
    );
  }

  Widget _buildEventCard(Map<String, dynamic> event, int index) {
    final eventDate = event['date'] as DateTime;
    final isCompleted = event['status'] == 'Completed';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getEventTypeColor(event['type']).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getEventTypeIcon(event['type']),
            color: _getEventTypeColor(event['type']),
            size: 24,
          ),
        ),
        title: Text(
          event['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
            decoration: isCompleted ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              event['description'],
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${_formatDate(eventDate)} • ${event['duration']}',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.people, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${event['attendees']} attendees',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getEventTypeColor(event['type']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    event['type'],
                    style: AppTheme.bodySmall.copyWith(
                      color: _getEventTypeColor(event['type']),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 14,
          color: AppTheme.textTertiary,
        ),
        onTap: () => _showEventDetails(event),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Color _getEventTypeColor(String type) {
    switch (type) {
      case 'Live Session':
        return Colors.red;
      case 'Workshop':
        return Colors.blue;
      case 'Doubt Session':
        return Colors.orange;
      case 'Revision':
        return Colors.green;
      case 'Discussion':
        return Colors.purple;
      default:
        return AppTheme.primaryColor;
    }
  }

  IconData _getEventTypeIcon(String type) {
    switch (type) {
      case 'Live Session':
        return Icons.videocam;
      case 'Workshop':
        return Icons.work;
      case 'Doubt Session':
        return Icons.help;
      case 'Revision':
        return Icons.refresh;
      case 'Discussion':
        return Icons.forum;
      default:
        return Icons.event;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    
    if (difference == 0) {
      return 'Today ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference == 1) {
      return 'Tomorrow ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference == -1) {
      return 'Yesterday ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.day}/${date.month}/${date.year} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    }
  }

  void _showEventDetails(Map<String, dynamic> event) {
    AppLogger.userAction('Event details viewed', event['title']);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event['title'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    event['description'],
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Event details and management options will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateEventDialog() {
    AppLogger.userAction('Create event dialog opened');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Event'),
        content: const Text('Event creation functionality will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Create event feature coming soon!'),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }
}
