import 'package:json_annotation/json_annotation.dart';

part 'auth_models.g.dart';

@JsonSerializable()
class LoginResponse {
  final String message;
  final bool success;

  LoginResponse({
    required this.message,
    this.success = true,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class AuthResponse {
  final String token;
  @JsonKey(name: 'active_session_id')
  final String activeSessionId;
  final UserData user;

  AuthResponse({
    required this.token,
    required this.activeSessionId,
    required this.user,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

@JsonSerializable()
class UserData {
  final String id;
  final String username;
  final String role;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  @JsonKey(name: 'center_code')
  final String? centerCode;
  @JsonKey(name: 'center_name')
  final String? centerName;
  @JsonKey(name: 'center_id')
  final String? centerId;
  final String? phone;
  final String? address;
  @JsonKey(name: 'student_id')
  final String? studentId;
  @JsonKey(name: 'course_id')
  final String? courseId;
  @JsonKey(name: 'course_name')
  final String? courseName;
  @JsonKey(name: 'subject_id')
  final String? subjectId;
  @JsonKey(name: 'subject_name')
  final String? subjectName;
  final String? designation;

  UserData({
    required this.id,
    required this.username,
    required this.role,
    this.firstName,
    this.lastName,
    this.centerCode,
    this.centerName,
    this.centerId,
    this.phone,
    this.address,
    this.studentId,
    this.courseId,
    this.courseName,
    this.subjectId,
    this.subjectName,
    this.designation,
  });

  factory UserData.fromJson(Map<String, dynamic> json) =>
      _$UserDataFromJson(json);

  Map<String, dynamic> toJson() => _$UserDataToJson(this);

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    if (firstName != null) return firstName!;
    if (centerName != null) return centerName!;
    return username;
  }

  String get roleDisplayName {
    switch (role) {
      case 'faculty':
        return 'Faculty';
      case 'kota_teacher':
        return 'Kota Teacher';
      case 'student':
        return 'Student';
      case 'director':
        return 'Director';
      case 'mendor':
        return 'Mentor';
      case 'center_counselor':
        return 'Center Counselor';
      case 'parent':
        return 'Parent';
      default:
        return role.toUpperCase();
    }
  }
}

@JsonSerializable()
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class OtpRequest {
  final String otp;

  OtpRequest({required this.otp});

  factory OtpRequest.fromJson(Map<String, dynamic> json) =>
      _$OtpRequestFromJson(json);

  Map<String, dynamic> toJson() => _$OtpRequestToJson(this);
}

@JsonSerializable()
class SessionValidationRequest {
  @JsonKey(name: 'user_id')
  final String userId;
  @JsonKey(name: 'active_session_id')
  final String activeSessionId;

  SessionValidationRequest({
    required this.userId,
    required this.activeSessionId,
  });

  factory SessionValidationRequest.fromJson(Map<String, dynamic> json) =>
      _$SessionValidationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SessionValidationRequestToJson(this);
}

@JsonSerializable()
class LogoutRequest {
  @JsonKey(name: 'user_id')
  final String userId;

  LogoutRequest({required this.userId});

  factory LogoutRequest.fromJson(Map<String, dynamic> json) =>
      _$LogoutRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LogoutRequestToJson(this);
}

/// Authentication state enum
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  otpRequired,
  error,
}

/// Authentication result class
class AuthResult {
  final bool success;
  final String? message;
  final AuthResponse? authResponse;
  final Exception? error;

  AuthResult({
    required this.success,
    this.message,
    this.authResponse,
    this.error,
  });

  factory AuthResult.success({
    String? message,
    AuthResponse? authResponse,
  }) {
    return AuthResult(
      success: true,
      message: message,
      authResponse: authResponse,
    );
  }

  factory AuthResult.failure({
    String? message,
    Exception? error,
  }) {
    return AuthResult(
      success: false,
      message: message,
      error: error,
    );
  }
}
