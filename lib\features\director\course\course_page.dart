import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class CoursePage extends StatelessWidget {
  const CoursePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Course Management',
      subtitle: 'Manage courses and curriculum',
      breadcrumbs: ['Dashboard', 'Director', 'Course'],
      featureName: 'Course Management',
    );
  }
}
