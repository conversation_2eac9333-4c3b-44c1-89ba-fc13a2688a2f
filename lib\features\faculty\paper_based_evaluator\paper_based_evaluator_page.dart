import 'package:flutter/material.dart';
import '../../../core/widgets/base_page.dart';
import '../../common/presentation/pages/feature_placeholder_page.dart';

class PaperBasedEvaluatorPage extends StatelessWidget {
  const PaperBasedEvaluatorPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const FeaturePlaceholderPage(
      title: 'Paper Based Evaluator',
      subtitle: 'Evaluate student papers and assignments',
      breadcrumbs: ['Dashboard', 'Faculty', 'Paper Based Evaluator'],
      featureName: 'Paper Based Evaluator',
    );
  }
}
