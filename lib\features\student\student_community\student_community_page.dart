import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class StudentCommunityPage extends StatefulWidget {
  const StudentCommunityPage({Key? key}) : super(key: key);

  @override
  State<StudentCommunityPage> createState() => _StudentCommunityPageState();
}

class _StudentCommunityPageState extends State<StudentCommunityPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  List<Map<String, dynamic>> _discussions = [];
  List<Map<String, dynamic>> _studyGroups = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCommunityData();
  }

  Future<void> _loadCommunityData() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _discussions = [
        {
          'id': '1',
          'title': 'Help with Organic Chemistry Mechanisms',
          'author': 'Priya Sharma',
          'subject': 'Chemistry',
          'replies': 12,
          'likes': 25,
          'timeAgo': '2 hours ago',
          'isAnswered': false,
          'tags': ['Organic', 'Mechanisms', 'JEE'],
        },
        {
          'id': '2',
          'title': 'Best approach for Physics numericals?',
          'author': 'Rahul Kumar',
          'subject': 'Physics',
          'replies': 8,
          'likes': 18,
          'timeAgo': '4 hours ago',
          'isAnswered': true,
          'tags': ['Physics', 'Numericals', 'Tips'],
        },
        {
          'id': '3',
          'title': 'Calculus integration tricks',
          'author': 'Ananya Patel',
          'subject': 'Mathematics',
          'replies': 15,
          'likes': 32,
          'timeAgo': '1 day ago',
          'isAnswered': true,
          'tags': ['Calculus', 'Integration', 'Math'],
        },
      ];
      
      _studyGroups = [
        {
          'id': '1',
          'name': 'JEE 2024 Physics Group',
          'members': 156,
          'subject': 'Physics',
          'description': 'Daily physics problem solving and doubt clearing',
          'isJoined': true,
          'lastActivity': '30 min ago',
        },
        {
          'id': '2',
          'name': 'NEET Biology Masters',
          'members': 203,
          'subject': 'Biology',
          'description': 'Comprehensive biology discussions and notes sharing',
          'isJoined': false,
          'lastActivity': '1 hour ago',
        },
        {
          'id': '3',
          'name': 'Chemistry Wizards',
          'members': 89,
          'subject': 'Chemistry',
          'description': 'Organic and inorganic chemistry problem solving',
          'isJoined': true,
          'lastActivity': '2 hours ago',
        },
      ];
      
      _isLoading = false;
    });
    
    AppLogger.userAction('Community data loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Student Community',
      subtitle: 'Connect, learn, and grow together',
      breadcrumbs: const ['Dashboard', 'Student', 'Student Community'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _createNewPost,
          icon: const Icon(Icons.add),
          tooltip: 'Create Post',
        ),
        IconButton(
          onPressed: _loadCommunityData,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: _buildCommunityContent(),
    );
  }

  Widget _buildCommunityContent() {
    return Column(
      children: [
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildDiscussionsTab(),
              _buildStudyGroupsTab(),
              _buildLeaderboardTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary,
        tabs: const [
          Tab(text: 'Discussions'),
          Tab(text: 'Study Groups'),
          Tab(text: 'Leaderboard'),
        ],
      ),
    );
  }

  Widget _buildDiscussionsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildQuickFilters(),
          const SizedBox(height: 16),
          if (_discussions.isEmpty)
            _buildEmptyState('No discussions', 'Start a new discussion to connect with peers')
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _discussions.length,
              itemBuilder: (context, index) {
                final discussion = _discussions[index];
                return _buildDiscussionCard(discussion, index);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildStudyGroupsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const SizedBox(height: 16),
          if (_studyGroups.isEmpty)
            _buildEmptyState('No study groups', 'Join or create study groups to collaborate')
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _studyGroups.length,
              itemBuilder: (context, index) {
                final group = _studyGroups[index];
                return _buildStudyGroupCard(group, index);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildLeaderboardHeader(),
          const SizedBox(height: 20),
          _buildLeaderboardList(),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    final filters = ['All', 'Physics', 'Chemistry', 'Mathematics', 'Biology'];
    
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = index == 0;
          
          return Container(
            margin: EdgeInsets.only(right: index < filters.length - 1 ? 8 : 0),
            child: FilterChip(
              label: Text(filter),
              selected: isSelected,
              onSelected: (selected) {
                AppLogger.userAction('Discussion filter selected', {'filter': filter});
              },
              selectedColor: AppTheme.primaryColor.withOpacity(0.2),
              checkmarkColor: AppTheme.primaryColor,
            ),
          );
        },
      ),
    );
  }

  Widget _buildDiscussionCard(Map<String, dynamic> discussion, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getSubjectColor(discussion['subject']).withOpacity(0.1),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            discussion['isAnswered'] ? Icons.check_circle : Icons.help_outline,
            color: discussion['isAnswered'] ? Colors.green : _getSubjectColor(discussion['subject']),
            size: 24,
          ),
        ),
        title: Text(
          discussion['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              'by ${discussion['author']} • ${discussion['timeAgo']}',
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.comment, size: 14, color: AppTheme.textTertiary),
                const SizedBox(width: 4),
                Text(
                  '${discussion['replies']}',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
                ),
                const SizedBox(width: 16),
                Icon(Icons.favorite, size: 14, color: AppTheme.textTertiary),
                const SizedBox(width: 4),
                Text(
                  '${discussion['likes']}',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getSubjectColor(discussion['subject']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    discussion['subject'],
                    style: AppTheme.bodySmall.copyWith(
                      color: _getSubjectColor(discussion['subject']),
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _openDiscussion(discussion),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildStudyGroupCard(Map<String, dynamic> group, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: _getSubjectColor(group['subject']).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.group,
            color: _getSubjectColor(group['subject']),
            size: 24,
          ),
        ),
        title: Text(
          group['name'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              group['description'],
              style: AppTheme.bodySmall.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.people, size: 14, color: AppTheme.textTertiary),
                const SizedBox(width: 4),
                Text(
                  '${group['members']} members',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 14, color: AppTheme.textTertiary),
                const SizedBox(width: 4),
                Text(
                  group['lastActivity'],
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textTertiary),
                ),
              ],
            ),
          ],
        ),
        trailing: ElevatedButton(
          onPressed: () => _toggleGroupMembership(group),
          style: ElevatedButton.styleFrom(
            backgroundColor: group['isJoined'] ? Colors.grey : AppTheme.primaryColor,
            foregroundColor: Colors.white,
            minimumSize: const Size(70, 32),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          child: Text(
            group['isJoined'] ? 'Joined' : 'Join',
            style: const TextStyle(fontSize: 12),
          ),
        ),
        onTap: () => _openStudyGroup(group),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildLeaderboardHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          const Icon(Icons.emoji_events, color: Colors.white, size: 32),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Community Leaderboard',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Top contributors this month',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboardList() {
    final leaders = [
      {'name': 'Arjun Mehta', 'points': 1250, 'rank': 1},
      {'name': 'Sneha Gupta', 'points': 1180, 'rank': 2},
      {'name': 'Vikram Singh', 'points': 1050, 'rank': 3},
      {'name': 'Priya Sharma', 'points': 980, 'rank': 4},
      {'name': 'Rahul Kumar', 'points': 920, 'rank': 5},
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: leaders.length,
      itemBuilder: (context, index) {
        final leader = leaders[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _getRankColor(leader['rank'] as int).withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Text(
                  '#${leader['rank']}',
                  style: AppTheme.bodyMedium.copyWith(
                    color: _getRankColor(leader['rank'] as int),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            title: Text(
              leader['name'] as String,
              style: AppTheme.bodyLarge.copyWith(
                color: AppTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            trailing: Text(
              '${leader['points']} pts',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ).animate(delay: Duration(milliseconds: 100 * index))
            .fadeIn()
            .slideX(begin: 0.2);
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(
            Icons.forum_outlined,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getSubjectColor(String subject) {
    switch (subject.toLowerCase()) {
      case 'physics':
        return Colors.blue;
      case 'chemistry':
        return Colors.green;
      case 'mathematics':
        return Colors.orange;
      case 'biology':
        return Colors.purple;
      default:
        return AppTheme.primaryColor;
    }
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // Gold
      case 2:
        return const Color(0xFFC0C0C0); // Silver
      case 3:
        return const Color(0xFFCD7F32); // Bronze
      default:
        return AppTheme.primaryColor;
    }
  }

  void _createNewPost() {
    AppLogger.userAction('Create new post clicked');
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Create new post feature coming soon!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _openDiscussion(Map<String, dynamic> discussion) {
    AppLogger.userAction('Discussion opened', {'discussion': discussion['title']});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening discussion: ${discussion['title']}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _openStudyGroup(Map<String, dynamic> group) {
    AppLogger.userAction('Study group opened', {'group': group['name']});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening study group: ${group['name']}'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _toggleGroupMembership(Map<String, dynamic> group) {
    AppLogger.userAction('Group membership toggled', {'group': group['name']});
    
    setState(() {
      group['isJoined'] = !group['isJoined'];
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          group['isJoined'] 
              ? 'Joined ${group['name']}' 
              : 'Left ${group['name']}',
        ),
        behavior: SnackBarBehavior.floating,
        backgroundColor: group['isJoined'] ? Colors.green : Colors.orange,
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
