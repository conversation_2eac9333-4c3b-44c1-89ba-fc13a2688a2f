import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/widgets/base_page.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/logger.dart';

class LiveStreamingPage extends StatefulWidget {
  const LiveStreamingPage({Key? key}) : super(key: key);

  @override
  State<LiveStreamingPage> createState() => _LiveStreamingPageState();
}

class _LiveStreamingPageState extends State<LiveStreamingPage> {
  bool _isLoading = false;
  bool _isLive = false;
  int _viewerCount = 0;
  String _streamStatus = 'Ready to Start';
  List<Map<String, dynamic>> _recentStreams = [];

  @override
  void initState() {
    super.initState();
    _loadStreamData();
  }

  Future<void> _loadStreamData() async {
    setState(() => _isLoading = true);
    
    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));
    
    setState(() {
      _recentStreams = [
        {
          'id': '1',
          'title': 'Physics - Mechanics',
          'date': DateTime.now().subtract(const Duration(hours: 2)),
          'duration': '1h 45m',
          'viewers': 156,
          'status': 'Completed',
        },
        {
          'id': '2',
          'title': 'Mathematics - Calculus',
          'date': DateTime.now().subtract(const Duration(days: 1)),
          'duration': '2h 15m',
          'viewers': 142,
          'status': 'Completed',
        },
        {
          'id': '3',
          'title': 'Chemistry - Organic',
          'date': DateTime.now().subtract(const Duration(days: 2)),
          'duration': '1h 30m',
          'viewers': 128,
          'status': 'Completed',
        },
      ];
      _isLoading = false;
    });
    
    AppLogger.userAction('Live Streaming data loaded');
  }

  @override
  Widget build(BuildContext context) {
    return BasePage(
      title: 'Live Streaming',
      subtitle: 'Broadcast live sessions to students',
      breadcrumbs: const ['Dashboard', 'Kota Teacher', 'Live Streaming'],
      isLoading: _isLoading,
      actions: [
        IconButton(
          onPressed: _loadStreamData,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
      child: _buildStreamingContent(),
    );
  }

  Widget _buildStreamingContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStreamControlCard(),
          const SizedBox(height: 20),
          _buildStreamStatsCard(),
          const SizedBox(height: 20),
          _buildQuickActions(),
          const SizedBox(height: 20),
          _buildRecentStreams(),
        ],
      ),
    );
  }

  Widget _buildStreamControlCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: _isLive ? 
          const LinearGradient(
            colors: [Colors.red, Colors.redAccent],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ) : AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: (_isLive ? Colors.red : AppTheme.primaryColor).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            _isLive ? Icons.stop_circle : Icons.play_circle_fill,
            size: 64,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          Text(
            _isLive ? 'LIVE' : 'Start Live Stream',
            style: AppTheme.headingLarge.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _streamStatus,
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          if (_isLive) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.visibility, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  '$_viewerCount viewers',
                  style: AppTheme.bodyMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _toggleStream,
              icon: Icon(_isLive ? Icons.stop : Icons.videocam),
              label: Text(_isLive ? 'Stop Stream' : 'Start Stream'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: _isLive ? Colors.red : AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildStreamStatsCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Stream Statistics',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('Total Streams', '24', Icons.videocam, Colors.blue),
              ),
              Expanded(
                child: _buildStatItem('Total Hours', '48h', Icons.access_time, Colors.green),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('Avg Viewers', '142', Icons.people, Colors.orange),
              ),
              Expanded(
                child: _buildStatItem('This Month', '8', Icons.calendar_month, Colors.purple),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn().slideY(begin: 0.1);
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      {'title': 'Schedule Stream', 'icon': Icons.schedule, 'color': Colors.blue},
      {'title': 'Stream Settings', 'icon': Icons.settings, 'color': Colors.grey},
      {'title': 'Recording History', 'icon': Icons.history, 'color': Colors.green},
      {'title': 'Analytics', 'icon': Icons.analytics, 'color': Colors.orange},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 3,
          ),
          itemCount: actions.length,
          itemBuilder: (context, index) {
            final action = actions[index];
            return ElevatedButton.icon(
              onPressed: () {
                AppLogger.userAction('Stream action tapped', {'title': action['title']});
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${action['title']} feature coming soon!'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              icon: Icon(
                action['icon'] as IconData,
                size: 18,
              ),
              label: Text(
                action['title'] as String,
                style: const TextStyle(fontSize: 12),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: (action['color'] as Color).withOpacity(0.1),
                foregroundColor: action['color'] as Color,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ).animate(delay: Duration(milliseconds: 100 * index))
                .fadeIn()
                .scale(begin: const Offset(0.8, 0.8));
          },
        ),
      ],
    );
  }

  Widget _buildRecentStreams() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Streams',
          style: AppTheme.headingSmall.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (_recentStreams.isEmpty)
          _buildEmptyStreams()
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _recentStreams.length,
            itemBuilder: (context, index) {
              final stream = _recentStreams[index];
              return _buildStreamCard(stream, index);
            },
          ),
      ],
    );
  }

  Widget _buildStreamCard(Map<String, dynamic> stream, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.play_circle_fill,
            color: Colors.red,
            size: 24,
          ),
        ),
        title: Text(
          stream['title'],
          style: AppTheme.bodyLarge.copyWith(
            color: AppTheme.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.access_time, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  _formatStreamDate(stream['date']),
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const SizedBox(width: 16),
                Icon(Icons.timer, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  stream['duration'],
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.visibility, size: 14, color: AppTheme.textSecondary),
                const SizedBox(width: 4),
                Text(
                  '${stream['viewers']} viewers',
                  style: AppTheme.bodySmall.copyWith(color: AppTheme.textSecondary),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    stream['status'],
                    style: AppTheme.bodySmall.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 14,
          color: AppTheme.textTertiary,
        ),
        onTap: () => _showStreamDetails(stream),
      ),
    ).animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn()
        .slideX(begin: 0.2);
  }

  Widget _buildEmptyStreams() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        children: [
          Icon(
            Icons.videocam_off,
            size: 48,
            color: AppTheme.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'No streams yet',
            style: AppTheme.headingSmall.copyWith(
              color: AppTheme.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start your first live stream to engage with students',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate().fadeIn().scale(begin: const Offset(0.8, 0.8));
  }

  String _formatStreamDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else {
      return '${difference.inDays} days ago';
    }
  }

  void _toggleStream() {
    setState(() {
      _isLive = !_isLive;
      if (_isLive) {
        _streamStatus = 'Broadcasting Live';
        _viewerCount = 0;
        _startViewerSimulation();
      } else {
        _streamStatus = 'Stream Ended';
        _viewerCount = 0;
      }
    });
    
    AppLogger.userAction(_isLive ? 'Stream started' : 'Stream stopped');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isLive ? 'Live stream started!' : 'Live stream stopped!'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: _isLive ? Colors.green : Colors.red,
      ),
    );
  }

  void _startViewerSimulation() {
    if (!_isLive) return;
    
    Future.delayed(const Duration(seconds: 2), () {
      if (_isLive && mounted) {
        setState(() {
          _viewerCount += (1 + (DateTime.now().millisecond % 3));
        });
        _startViewerSimulation();
      }
    });
  }

  void _showStreamDetails(Map<String, dynamic> stream) {
    AppLogger.userAction('Stream details viewed', stream['title']);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.textTertiary,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stream['title'],
                    style: AppTheme.headingMedium.copyWith(
                      color: AppTheme.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Stream analytics and detailed information will be available here.',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
